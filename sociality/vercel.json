{"version": 2, "builds": [{"src": "backend/server.js", "use": "@vercel/node"}, {"src": "frontend/package.json", "use": "@vercel/static-build", "config": {"distDir": "dist"}}], "routes": [{"src": "/api/(.*)", "dest": "/backend/server.js"}, {"src": "/socket.io/(.*)", "dest": "/backend/server.js"}, {"src": "/(.*)", "dest": "/frontend/$1"}], "env": {"NODE_ENV": "production"}, "functions": {"backend/server.js": {"maxDuration": 30}}}